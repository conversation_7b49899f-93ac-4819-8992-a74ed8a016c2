<?php
/**
 * Dakoii Provincial Government Theme functions and definitions
 *
 * @package Dakoii_Provincial_Government_Theme
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme setup
 */
function nols_espa_theme_setup() {
    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support('post-thumbnails');

    // Add support for responsive embedded content
    add_theme_support('responsive-embeds');

    // Add support for editor styles
    add_theme_support('editor-styles');

    // Add support for wide alignment
    add_theme_support('align-wide');

    // Add support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 60,
        'width'       => 60,
        'flex-width'  => true,
        'flex-height' => true,
    ));

    // Add support for custom header
    add_theme_support('custom-header', array(
        'default-color'      => 'CE1126',
        'width'              => 1200,
        'height'             => 400,
        'flex-width'         => true,
        'flex-height'        => true,
        'header-text'        => true,  // Enable header text to ensure full functionality
        'uploads'            => true,  // Enable uploads
        'random-default'     => false,
        'default-text-color' => 'CE1126',
        'wp-head-callback'   => 'nols_espa_header_style',
    ));

    // Add support for custom background
    add_theme_support('custom-background', array(
        'default-color'      => '',
        'default-image'      => '',
        'default-repeat'     => 'no-repeat',
        'default-position-x' => 'center',
        'default-position-y' => 'center',
        'default-size'       => 'cover',
        'default-attachment' => 'fixed',
        'wp-head-callback'   => 'nols_espa_custom_background_cb',
    ));

    // Add support for HTML5 markup
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));

    // Add support for selective refresh for widgets
    add_theme_support('customize-selective-refresh-widgets');

    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', 'dakoii-provincial-government-theme'),
    ));

    // Set content width
    if (!isset($content_width)) {
        $content_width = 800;
    }
}
add_action('after_setup_theme', 'nols_espa_theme_setup');

/**
 * Custom header style callback
 */
function nols_espa_header_style() {
    $header_text_color = get_header_textcolor();

    // If no custom color is set, return early
    if (get_theme_support('custom-header', 'default-text-color') === $header_text_color) {
        return;
    }

    // If we get this far, we have custom styles
    ?>
    <style type="text/css">
    <?php if (!display_header_text()) : ?>
        .site-title,
        .site-description {
            position: absolute;
            clip: rect(1px, 1px, 1px, 1px);
        }
    <?php else : ?>
        .site-title a,
        .site-description {
            color: #<?php echo esc_attr($header_text_color); ?>;
        }
    <?php endif; ?>
    </style>
    <?php
}

/**
 * Register widget areas
 */
function nols_espa_theme_widgets_init() {
    register_sidebar(array(
        'name'          => esc_html__('Primary Sidebar', 'dakoii-provincial-government-theme'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Add widgets here to appear in your sidebar.', 'dakoii-provincial-government-theme'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => esc_html__('Footer Widgets', 'dakoii-provincial-government-theme'),
        'id'            => 'footer-widgets',
        'description'   => esc_html__('Add widgets here to appear in your footer.', 'dakoii-provincial-government-theme'),
        'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'nols_espa_theme_widgets_init');

/**
 * Enqueue scripts and styles
 */
function nols_espa_theme_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style('nols-espa-theme-style', get_stylesheet_uri(), array(), '1.0.0');

    // Enqueue Google Fonts - Quicksand
    wp_enqueue_style('nols-espa-theme-fonts', 'https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap', array(), null);

    // Enqueue theme JavaScript
    wp_enqueue_script('nols-espa-theme-script', get_template_directory_uri() . '/js/theme.js', array(), '1.0.0', true);

    // Enqueue comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'nols_espa_theme_scripts');

/**
 * Enqueue customizer scripts (native WordPress approach)
 */
function nols_espa_customizer_scripts() {
    // Only enqueue basic media scripts - let WordPress handle everything natively
    wp_enqueue_media();
}
add_action('customize_controls_enqueue_scripts', 'nols_espa_customizer_scripts');

/**
 * Clear invalid header image URL on theme activation
 */
function nols_espa_clear_invalid_header_image() {
    $header_image = get_header_image();

    // Check if header image URL is invalid (contains ******* or other invalid URLs)
    if ($header_image && (strpos($header_image, '*******') !== false || strpos($header_image, 'http://0.0.0.') !== false)) {
        // Clear the invalid header image
        remove_theme_mod('header_image');
        remove_theme_mod('header_image_data');
    }
}
add_action('after_switch_theme', 'nols_espa_clear_invalid_header_image');



/**
 * Custom excerpt length
 */
function nols_espa_theme_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'nols_espa_theme_excerpt_length');

/**
 * Custom excerpt more
 */
function nols_espa_theme_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'nols_espa_theme_excerpt_more');

/**
 * Add custom classes to body
 */
function nols_espa_theme_body_classes($classes) {
    // Add class for cultural theme
    $classes[] = 'cultural-theme';
    
    // Add class for PNG colors
    $classes[] = 'png-colors';
    
    return $classes;
}
add_filter('body_class', 'nols_espa_theme_body_classes');

/**
 * Customize comment form
 */
function nols_espa_theme_comment_form_defaults($defaults) {
    $defaults['comment_notes_before'] = '<p class="comment-notes">' . esc_html__('Your email address will not be published. Required fields are marked *', 'dakoii-provincial-government-theme') . '</p>';
    $defaults['comment_notes_after'] = '';
    $defaults['title_reply'] = esc_html__('Leave a Comment', 'dakoii-provincial-government-theme');
    $defaults['title_reply_to'] = esc_html__('Leave a Reply to %s', 'dakoii-provincial-government-theme');
    $defaults['cancel_reply_link'] = esc_html__('Cancel Reply', 'dakoii-provincial-government-theme');
    $defaults['label_submit'] = esc_html__('Post Comment', 'dakoii-provincial-government-theme');
    
    return $defaults;
}
add_filter('comment_form_defaults', 'nols_espa_theme_comment_form_defaults');

/**
 * Add cultural pattern widget
 */
class Nols_ESPA_Cultural_Pattern_Widget extends WP_Widget {
    
    public function __construct() {
        parent::__construct(
            'nols_espa_cultural_pattern',
            esc_html__('Cultural Pattern', 'dakoii-provincial-government-theme'),
            array('description' => esc_html__('Display a cultural pattern design element.', 'dakoii-provincial-government-theme'))
        );
    }
    
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }
        
        echo '<div class="cultural-pattern"></div>';
        
        echo $args['after_widget'];
    }
    
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : esc_html__('Cultural Pattern', 'dakoii-provincial-government-theme');
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php esc_attr_e('Title:', 'dakoii-provincial-government-theme'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
    }
    
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        return $instance;
    }
}

/**
 * Register cultural pattern widget
 */
function nols_espa_theme_register_widgets() {
    register_widget('Nols_ESPA_Cultural_Pattern_Widget');
}
add_action('widgets_init', 'nols_espa_theme_register_widgets');

/**
 * Get available color combinations
 */
function nols_espa_get_color_combinations() {
    return array(
        'traditional' => array(
            'label' => esc_html__('Traditional PNG Flag', 'dakoii-provincial-government-theme'),
            'colors' => array(
                'red' => '#DC143C',
                'green' => '#000000',
                'yellow' => '#FFD700',
            ),
        ),
        'sepik_heritage' => array(
            'label' => esc_html__('Sepik River Heritage', 'dakoii-provincial-government-theme'),
            'colors' => array(
                'red' => '#B8860B',
                'green' => '#228B22',
                'yellow' => '#DAA520',
            ),
        ),
        'cultural_celebration' => array(
            'label' => esc_html__('Cultural Celebration', 'dakoii-provincial-government-theme'),
            'colors' => array(
                'red' => '#DC143C',
                'green' => '#008B8B',
                'yellow' => '#FF8C00',
            ),
        ),
    );
}

/**
 * Color manipulation helper functions
 */
function nols_espa_hex_to_rgb($hex) {
    $hex = ltrim($hex, '#');
    return array(
        'r' => hexdec(substr($hex, 0, 2)),
        'g' => hexdec(substr($hex, 2, 2)),
        'b' => hexdec(substr($hex, 4, 2)),
    );
}

function nols_espa_rgb_to_hex($r, $g, $b) {
    return sprintf('#%02x%02x%02x', $r, $g, $b);
}

function nols_espa_darken_color($hex, $percent) {
    $rgb = nols_espa_hex_to_rgb($hex);
    $factor = (100 - $percent) / 100;

    $r = max(0, min(255, round($rgb['r'] * $factor)));
    $g = max(0, min(255, round($rgb['g'] * $factor)));
    $b = max(0, min(255, round($rgb['b'] * $factor)));

    return nols_espa_rgb_to_hex($r, $g, $b);
}

function nols_espa_lighten_color($hex, $percent) {
    $rgb = nols_espa_hex_to_rgb($hex);
    $factor = $percent / 100;

    $r = max(0, min(255, round($rgb['r'] + (255 - $rgb['r']) * $factor)));
    $g = max(0, min(255, round($rgb['g'] + (255 - $rgb['g']) * $factor)));
    $b = max(0, min(255, round($rgb['b'] + (255 - $rgb['b']) * $factor)));

    return nols_espa_rgb_to_hex($r, $g, $b);
}

/**
 * Special handling for black color variations
 */
function nols_espa_get_black_variations($base_color) {
    $rgb = nols_espa_hex_to_rgb($base_color);

    // If it's very dark (close to black), create variations differently
    if ($rgb['r'] < 30 && $rgb['g'] < 30 && $rgb['b'] < 30) {
        return array(
            'dark' => '#000000',     // Pure black for dark variant
            'light' => '#333333',    // Dark gray for light variant
        );
    }

    // For other colors, use normal calculation
    return array(
        'dark' => nols_espa_darken_color($base_color, 25),
        'light' => nols_espa_lighten_color($base_color, 30),
    );
}

/**
 * Get current color values with derived colors
 */
function nols_espa_get_current_colors() {
    $combination = get_theme_mod('color_combination', 'traditional');
    $combinations = nols_espa_get_color_combinations();

    // Get base colors
    if ($combination === 'custom') {
        $red = get_theme_mod('png_red_color', '#CE1126');
        $green = get_theme_mod('png_green_color', '#006A4E');
        $yellow = get_theme_mod('png_yellow_color', '#FFD700');
    } else {
        $colors = isset($combinations[$combination]) ? $combinations[$combination]['colors'] : $combinations['traditional']['colors'];
        $red = $colors['red'];
        $green = $colors['green'];
        $yellow = $colors['yellow'];
    }

    // Calculate derived colors with special handling for black
    $green_variations = nols_espa_get_black_variations($green);

    // Calculate text colors that work well with the selected theme
    $text_primary = ($green === '#000000') ? '#333333' : nols_espa_darken_color($green, 80);
    $text_secondary = ($green === '#000000') ? '#666666' : nols_espa_darken_color($green, 60);
    $text_muted = ($green === '#000000') ? '#999999' : nols_espa_darken_color($green, 40);

    return array(
        'png-red' => $red,
        'png-green' => $green,
        'png-yellow' => $yellow,
        'dark-green' => $green_variations['dark'],
        'light-green' => $green_variations['light'],
        'cream' => '#FFF8DC',
        'dark-brown' => '#8B4513',
        'official-blue' => '#1e3a8a',
        'light-gray' => '#f8fafc',
        'medium-gray' => '#64748b',
        'text-primary' => $text_primary,
        'text-secondary' => $text_secondary,
        'text-muted' => $text_muted,
        'border-light' => '#eeeeee',
        'border-medium' => '#cccccc',
        'white' => '#ffffff',
        'black' => '#000000',
        // Pattern colors for SVG backgrounds
        'pattern-yellow-light' => nols_espa_lighten_color($yellow, 20),
        'pattern-green-light' => nols_espa_lighten_color($green, 20),
    );
}

/**
 * Add theme customizer options
 */
function nols_espa_theme_customize_register($wp_customize) {
    // Add cultural colors section
    $wp_customize->add_section('nols_espa_cultural_colors', array(
        'title'    => esc_html__('Cultural Colors', 'dakoii-provincial-government-theme'),
        'priority' => 30,
        'description' => esc_html__('Choose from predefined color combinations or customize individual colors.', 'dakoii-provincial-government-theme'),
    ));

    // Color combination selector
    $wp_customize->add_setting('color_combination', array(
        'default'           => 'traditional',
        'sanitize_callback' => 'nols_espa_sanitize_color_combination',
    ));

    $combinations = nols_espa_get_color_combinations();
    $combination_choices = array('custom' => esc_html__('Custom Colors', 'dakoii-provincial-government-theme'));
    foreach ($combinations as $key => $combination) {
        $combination_choices[$key] = $combination['label'];
    }

    $wp_customize->add_control('color_combination', array(
        'label'    => esc_html__('Color Combination', 'dakoii-provincial-government-theme'),
        'section'  => 'nols_espa_cultural_colors',
        'settings' => 'color_combination',
        'type'     => 'select',
        'choices'  => $combination_choices,
        'priority' => 10,
    ));

    // PNG Red color
    $wp_customize->add_setting('png_red_color', array(
        'default'           => '#DC143C',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'png_red_color', array(
        'label'    => esc_html__('PNG Red Color', 'dakoii-provincial-government-theme'),
        'section'  => 'nols_espa_cultural_colors',
        'settings' => 'png_red_color',
        'priority' => 20,
    )));

    // PNG Black color (was green)
    $wp_customize->add_setting('png_green_color', array(
        'default'           => '#000000',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'png_green_color', array(
        'label'    => esc_html__('PNG Black Color', 'dakoii-provincial-government-theme'),
        'section'  => 'nols_espa_cultural_colors',
        'settings' => 'png_green_color',
        'priority' => 30,
    )));

    // PNG Gold color (was yellow)
    $wp_customize->add_setting('png_yellow_color', array(
        'default'           => '#FFD700',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'png_yellow_color', array(
        'label'    => esc_html__('PNG Gold Color', 'dakoii-provincial-government-theme'),
        'section'  => 'nols_espa_cultural_colors',
        'settings' => 'png_yellow_color',
        'priority' => 40,
    )));





    // WordPress native background support will handle background image controls automatically
}
add_action('customize_register', 'nols_espa_theme_customize_register');

/**
 * Clear only legacy custom header image data on theme activation
 */
function nols_espa_clear_legacy_header_data() {
    // Only clear legacy custom header image data from previous implementation
    remove_theme_mod('custom_header_image');

    // Clear any other legacy data that might conflict
    remove_theme_mod('header_image_url');
    remove_theme_mod('header_image_width');
    remove_theme_mod('header_image_height');
}
add_action('after_switch_theme', 'nols_espa_clear_legacy_header_data');


/**
 * Enqueue scripts for customizer preview
 */
function nols_espa_customize_preview_scripts() {
    wp_enqueue_script('customize-preview');

    // Add custom preview script for better customizer functionality
    wp_add_inline_script('customize-preview', '
        (function($) {
            $(document).ready(function() {
                // Preview scripts loaded - ready for customizer functionality
            });
        })(jQuery);
    ');
}
add_action('customize_preview_init', 'nols_espa_customize_preview_scripts');



/**
 * Sanitize color combination choice
 */
function nols_espa_sanitize_color_combination($input) {
    $valid_combinations = array_keys(nols_espa_get_color_combinations());
    $valid_combinations[] = 'custom';

    return in_array($input, $valid_combinations) ? $input : 'traditional';
}



/**
 * Output dynamic CSS for color combinations
 */
function nols_espa_output_dynamic_colors() {
    $colors = nols_espa_get_current_colors();

    echo '<style id="nols-espa-dynamic-colors">';
    echo ':root {';
    foreach ($colors as $property => $value) {
        echo '--' . esc_attr($property) . ': ' . esc_attr($value) . ';';
    }
    echo '}';
    echo '</style>';
}
add_action('wp_head', 'nols_espa_output_dynamic_colors', 100);

/**
 * Output dynamic CSS for customizer preview
 */
function nols_espa_output_customizer_colors() {
    if (is_customize_preview()) {
        $colors = nols_espa_get_current_colors();

        echo '<style id="nols-espa-customizer-colors">';
        echo ':root {';
        foreach ($colors as $property => $value) {
            echo '--' . esc_attr($property) . ': ' . esc_attr($value) . ';';
        }
        echo '}';
        echo '</style>';
    }
}
add_action('wp_head', 'nols_espa_output_customizer_colors', 101);

/**
 * Custom background callback function
 * This replaces WordPress default background output with our enhanced version
 */
function nols_espa_custom_background_cb() {
    // Get WordPress native background settings
    $background_color = get_background_color();
    $background_image = get_background_image();

    $colors = nols_espa_get_current_colors();

    // Start building the style
    $style = '';

    if ($background_image) {
        // Use WordPress native background image
        $image = esc_url($background_image);
        $repeat = get_theme_mod('background_repeat', get_theme_support('custom-background', 'default-repeat'));
        $position = get_theme_mod('background_position_x', get_theme_support('custom-background', 'default-position-x'));
        $attachment = get_theme_mod('background_attachment', get_theme_support('custom-background', 'default-attachment'));
        $size = get_theme_mod('background_size', get_theme_support('custom-background', 'default-size'));

        $style .= "background-image: url('$image');";
        $style .= "background-repeat: $repeat;";
        $style .= "background-position: $position;";
        $style .= "background-attachment: $attachment;";
        if ($size) {
            $style .= "background-size: $size;";
        }

        // Add background color if set
        if ($background_color) {
            $style .= "background-color: #$background_color;";
        }

    } else {
        // No background image - use cultural gradient fallback
        if ($background_color) {
            $style .= "background-color: #$background_color;";
        } else {
            // Use cultural gradient as fallback
            $style .= 'background: linear-gradient(135deg, ' . esc_attr($colors['png-green']) . ' 0%, ' . esc_attr($colors['dark-green']) . ' 100%);';
        }
    }

    if ($style) {
        echo '<style type="text/css" id="custom-background-css">body.custom-background { ' . $style . ' }</style>';
    }
}

/**
 * Clean up any invalid background settings on theme activation
 */
function nols_espa_cleanup_background_on_activation() {
    // Remove any custom background settings that might conflict
    remove_theme_mod('background_image');
    remove_theme_mod('background_color');
    remove_theme_mod('bg_image_url');

    // Clear any invalid WordPress native background settings
    $bg_image = get_background_image();
    if ($bg_image && strpos($bg_image, '*******') !== false) {
        remove_theme_mod('background_image_thumb');
        set_theme_mod('background_image', '');
    }
}
add_action('after_switch_theme', 'nols_espa_cleanup_background_on_activation');
